name: Sub-Store
desc: 高级订阅管理工具 @Peng-YM. 定时任务默认为每天  23 点 55 分. 定时任务指定时将订阅/文件上传到私有 Gist. 在前端, 叫做 '同步' 或 '同步配置'
icon: https://raw.githubusercontent.com/cc63/ICON/main/Sub-Store.png

http:
  mitm:
    - sub.store
  script:
    - match: ^https?:\/\/sub\.store\/((download)|api\/(preview|sync|(utils\/node-info)))
      name: sub-store-1
      type: request
      require-body: true
      timeout: 120
    - match: ^https?:\/\/sub\.store
      name: sub-store-0
      type: request
      require-body: true
      timeout: 120

cron:
  script:
    - name: cron-sync-artifacts
      cron: "55 23 * * *"
      timeout: 120

script-providers:
  sub-store-0:
    url: https://raw.githubusercontent.com/sub-store-org/Sub-Store/release/sub-store-0.min.js
    interval: 86400

  sub-store-1:
    url: https://raw.githubusercontent.com/sub-store-org/Sub-Store/release/sub-store-1.min.js
    interval: 86400

  cron-sync-artifacts:
    url: https://raw.githubusercontent.com/sub-store-org/Sub-Store/release/cron-sync-artifacts.min.js
    interval: 86400
