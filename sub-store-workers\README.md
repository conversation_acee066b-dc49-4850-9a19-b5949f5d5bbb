# Sub-Store Workers

基于Cloudflare Workers的高级订阅管理器，支持多种代理协议和客户端格式转换。

## 功能特性

- 🚀 **高性能**: 基于Cloudflare Workers，全球边缘计算
- 🔄 **格式转换**: 支持Clash、Surge、QuantumultX等多种格式
- 🛡️ **协议支持**: SS、SSR、VMess、VLESS、Trojan等主流协议
- 📊 **订阅管理**: 完整的订阅CRUD操作和流量监控
- 🎯 **智能过滤**: 正则、地区、类型、脚本等多种过滤方式
- 📦 **集合功能**: 多订阅聚合和统一管理
- 💾 **数据持久化**: 基于Cloudflare KV存储

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd sub-store-workers
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置Cloudflare

#### 创建KV命名空间

```bash
# 创建生产环境KV命名空间
wrangler kv:namespace create "SUB_STORE_KV"

# 创建预览环境KV命名空间
wrangler kv:namespace create "SUB_STORE_KV" --preview
```

#### 更新wrangler.toml

将创建的KV命名空间ID更新到`wrangler.toml`文件中：

```toml
[[kv_namespaces]]
binding = "SUB_STORE_KV"
id = "your-production-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"
```

### 4. 本地开发

```bash
npm run dev
```

### 5. 部署

```bash
# 部署到开发环境
npm run deploy:dev

# 部署到生产环境
npm run deploy:prod
```

## API文档

### 订阅管理

#### 获取所有订阅
```http
GET /api/subs
```

#### 创建订阅
```http
POST /api/subs
Content-Type: application/json

{
  "name": "订阅名称",
  "url": "订阅URL",
  "tag": "标签",
  "ua": "User-Agent",
  "process": []
}
```

#### 获取单个订阅
```http
GET /api/sub/{name}
```

#### 更新订阅
```http
PATCH /api/sub/{name}
Content-Type: application/json

{
  "url": "新的订阅URL",
  "process": [...]
}
```

#### 删除订阅
```http
DELETE /api/sub/{name}
```

#### 获取订阅流量信息
```http
GET /api/sub/flow/{name}
```

### 集合管理

#### 获取所有集合
```http
GET /api/collections
```

#### 创建集合
```http
POST /api/collections
Content-Type: application/json

{
  "name": "集合名称",
  "subscriptions": ["订阅1", "订阅2"],
  "process": []
}
```

### 下载转换

#### 下载订阅
```http
GET /download/sub/{name}?target=clash&format=yaml
```

#### 下载集合
```http
GET /download/collection/{name}?target=surge&format=conf
```

支持的格式：
- `clash` - Clash配置
- `surge` - Surge配置  
- `quantumult-x` / `qx` - QuantumultX配置
- `uri` - 原始URI格式

### 设置管理

#### 获取设置
```http
GET /api/settings
```

#### 更新设置
```http
PATCH /api/settings
Content-Type: application/json

{
  "userAgent": "Custom-UA",
  "timeout": 15000
}
```

## 处理器配置

### 正则过滤器
```json
{
  "type": "Regex Filter",
  "pattern": "香港|HK",
  "flags": "i"
}
```

### 地区过滤器
```json
{
  "type": "Region Filter",
  "regions": ["HK", "TW", "SG"],
  "mode": "include"
}
```

### 类型过滤器
```json
{
  "type": "Type Filter",
  "types": ["vmess", "trojan"],
  "mode": "include"
}
```

### 排序处理器
```json
{
  "type": "Sort",
  "order": "asc"
}
```

### 重命名处理器
```json
{
  "type": "Rename",
  "pattern": "\\[.*?\\]",
  "replacement": ""
}
```

### 脚本处理器
```json
{
  "type": "Script",
  "script": "proxies.forEach(p => p.name = p.name.toUpperCase());"
}
```

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `ENVIRONMENT` | 运行环境 | `production` |
| `VERSION` | 版本号 | `2.0.0` |
| `CORS_ORIGIN` | CORS源 | `*` |

## 部署注意事项

1. **KV命名空间**: 确保正确配置KV命名空间ID
2. **域名绑定**: 可以绑定自定义域名以获得更好的访问体验
3. **访问限制**: 根据需要配置Workers的访问限制
4. **监控**: 建议启用Cloudflare的分析和监控功能

## 许可证

GPL-3.0 License

## 贡献

欢迎提交Issue和Pull Request！

## 支持

如果这个项目对您有帮助，请给个⭐️！
