{"name": "sub-store-workers", "version": "2.0.0", "description": "Sub-Store for Cloudflare Workers - Advanced Subscription Manager", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env dev", "deploy:prod": "wrangler deploy --env production", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["proxy", "subscription", "cloudflare-workers", "clash", "surge", "quantumult-x"], "author": "Sub-Store Workers", "license": "GPL-3.0", "devDependencies": {"@cloudflare/workers-types": "^4.20240117.0", "wrangler": "^3.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "jest": "^29.0.0"}, "dependencies": {"js-base64": "^3.7.5", "js-yaml": "^4.1.0"}}