# 部署指南

本文档详细介绍如何将Sub-Store Workers部署到Cloudflare Workers。

## 前置要求

1. Cloudflare账户
2. Node.js 16+
3. npm或yarn包管理器

## 详细部署步骤

### 1. 准备Cloudflare账户

1. 注册/登录 [Cloudflare](https://cloudflare.com)
2. 获取API Token：
   - 进入 `My Profile` > `API Tokens`
   - 点击 `Create Token`
   - 使用 `Custom token` 模板
   - 权限设置：
     - `Zone:Zone:Read`
     - `Zone:Zone Settings:Edit`
     - `Account:Cloudflare Workers:Edit`
   - 账户资源：包含所有账户
   - 区域资源：包含所有区域

### 2. 安装Wrangler CLI

```bash
npm install -g wrangler
```

### 3. 登录Cloudflare

```bash
wrangler login
```

或使用API Token：

```bash
export CLOUDFLARE_API_TOKEN=your-api-token
```

### 4. 创建KV命名空间

```bash
# 创建生产环境KV命名空间
wrangler kv:namespace create "SUB_STORE_KV"

# 创建预览环境KV命名空间  
wrangler kv:namespace create "SUB_STORE_KV" --preview
```

命令执行后会返回类似以下内容：

```
🌀 Creating namespace with title "sub-store-workers-SUB_STORE_KV"
✨ Success!
Add the following to your configuration file in your kv_namespaces array:
{ binding = "SUB_STORE_KV", id = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" }
```

### 5. 更新配置文件

将获得的KV命名空间ID更新到`wrangler.toml`：

```toml
name = "sub-store-workers"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[[kv_namespaces]]
binding = "SUB_STORE_KV"
id = "your-production-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"

[vars]
ENVIRONMENT = "production"
VERSION = "2.0.0"
CORS_ORIGIN = "*"
```

### 6. 本地测试

```bash
# 安装依赖
npm install

# 启动本地开发服务器
npm run dev
```

访问 `http://localhost:8787` 测试API：

```bash
# 健康检查
curl http://localhost:8787/health

# API信息
curl http://localhost:8787/api/info
```

### 7. 部署到Cloudflare

```bash
# 部署到生产环境
npm run deploy:prod

# 或者使用wrangler直接部署
wrangler deploy
```

部署成功后会显示Workers的URL：

```
✨ Success! Uploaded 1 files (x.xx sec)
✨ Deployment complete! Take a flight over to https://sub-store-workers.your-subdomain.workers.dev to see your worker in action.
```

## 高级配置

### 自定义域名

1. 在Cloudflare Dashboard中进入Workers页面
2. 选择你的Worker
3. 点击 `Triggers` 标签
4. 在 `Custom Domains` 部分添加自定义域名

### 环境变量配置

在`wrangler.toml`中配置环境变量：

```toml
[vars]
ENVIRONMENT = "production"
VERSION = "2.0.0"
CORS_ORIGIN = "https://yourdomain.com"
USER_AGENT = "Sub-Store-Workers/2.0.0"
TIMEOUT = "10000"
```

### 多环境部署

#### 开发环境配置

```toml
[env.dev]
name = "sub-store-workers-dev"
vars = { ENVIRONMENT = "development", CORS_ORIGIN = "*" }
```

#### 生产环境配置

```toml
[env.production]  
name = "sub-store-workers-prod"
vars = { ENVIRONMENT = "production", CORS_ORIGIN = "https://yourdomain.com" }
```

部署到不同环境：

```bash
# 部署到开发环境
wrangler deploy --env dev

# 部署到生产环境
wrangler deploy --env production
```

## 监控和日志

### 查看日志

```bash
# 实时查看日志
wrangler tail

# 查看特定环境的日志
wrangler tail --env production
```

### 监控指标

在Cloudflare Dashboard的Workers页面可以查看：

- 请求数量
- 错误率
- 响应时间
- CPU使用时间

## 故障排除

### 常见问题

1. **KV命名空间未找到**
   - 检查`wrangler.toml`中的KV命名空间ID是否正确
   - 确保KV命名空间已创建

2. **部署失败**
   - 检查Wrangler是否已登录：`wrangler whoami`
   - 检查API Token权限是否正确

3. **CORS错误**
   - 检查`CORS_ORIGIN`环境变量设置
   - 确保请求头包含正确的Origin

4. **代理解析失败**
   - 检查订阅URL是否可访问
   - 验证订阅内容格式是否正确

### 调试技巧

1. **本地调试**
   ```bash
   # 启用详细日志
   wrangler dev --local --log-level debug
   ```

2. **远程调试**
   ```bash
   # 查看实时日志
   wrangler tail --format pretty
   ```

3. **测试API**
   ```bash
   # 测试健康检查
   curl https://your-worker.workers.dev/health
   
   # 测试订阅创建
   curl -X POST https://your-worker.workers.dev/api/subs \
     -H "Content-Type: application/json" \
     -d '{"name":"test","url":"https://example.com/sub"}'
   ```

## 性能优化

1. **缓存策略**
   - 合理设置KV存储的TTL
   - 使用Cloudflare缓存API

2. **请求优化**
   - 批量处理订阅更新
   - 异步处理长时间任务

3. **资源限制**
   - Workers有CPU时间限制（10ms免费版，50ms付费版）
   - 内存限制128MB
   - 请求大小限制100MB

## 安全建议

1. **访问控制**
   - 配置适当的CORS策略
   - 考虑添加API密钥验证

2. **数据保护**
   - 敏感数据加密存储
   - 定期备份KV数据

3. **监控告警**
   - 设置异常请求监控
   - 配置错误率告警

## 成本估算

Cloudflare Workers定价（2024年）：

- **免费版**：100,000请求/天
- **付费版**：$5/月 + $0.50/百万请求

KV存储定价：

- **免费版**：100,000读取/天，1,000写入/天
- **付费版**：$0.50/百万读取，$5.00/百万写入

根据使用量选择合适的计划。
