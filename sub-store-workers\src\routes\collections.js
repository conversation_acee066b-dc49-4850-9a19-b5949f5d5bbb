/**
 * 集合管理API路由
 */

import { Storage, Database, STORAGE_KEYS } from '../core/storage.js';
import { successResponse, errorResponse } from '../utils/response.js';

export default function collectionRoutes(router, env) {
  const storage = new Storage(env.SUB_STORE_KV);
  const db = new Database(storage);

  // 获取所有集合
  router.get('/api/collections', async (req) => {
    try {
      const collections = await db.getAll(STORAGE_KEYS.COLLECTIONS);
      return successResponse(collections);
    } catch (error) {
      return errorResponse('Failed to get collections: ' + error.message, 500);
    }
  });

  // 创建集合
  router.post('/api/collections', async (req) => {
    try {
      const data = await req.json();
      
      if (!data.name || !data.subscriptions) {
        return errorResponse('Name and subscriptions are required', 400);
      }

      // 检查集合是否已存在
      const existing = await db.findByName(STORAGE_KEYS.COLLECTIONS, data.name);
      if (existing) {
        return errorResponse('Collection already exists', 409);
      }

      const collection = {
        name: data.name,
        subscriptions: data.subscriptions || [],
        process: data.process || [],
        tag: data.tag || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const created = await db.create(STORAGE_KEYS.COLLECTIONS, data.name, collection);
      return successResponse(created, 201);
    } catch (error) {
      return errorResponse('Failed to create collection: ' + error.message, 500);
    }
  });

  // 获取单个集合
  router.get('/api/collection/:name', async (req) => {
    try {
      const { name } = req.params;
      const collection = await db.findByName(STORAGE_KEYS.COLLECTIONS, decodeURIComponent(name));
      
      if (!collection) {
        return errorResponse('Collection not found', 404);
      }

      return successResponse(collection);
    } catch (error) {
      return errorResponse('Failed to get collection: ' + error.message, 500);
    }
  });

  // 更新集合
  router.patch('/api/collection/:name', async (req) => {
    try {
      const { name } = req.params;
      const data = await req.json();
      
      const existing = await db.findByName(STORAGE_KEYS.COLLECTIONS, decodeURIComponent(name));
      if (!existing) {
        return errorResponse('Collection not found', 404);
      }

      const updated = {
        ...data,
        updatedAt: new Date().toISOString()
      };

      const result = await db.updateByName(STORAGE_KEYS.COLLECTIONS, decodeURIComponent(name), updated);
      return successResponse(result);
    } catch (error) {
      return errorResponse('Failed to update collection: ' + error.message, 500);
    }
  });

  // 删除集合
  router.delete('/api/collection/:name', async (req) => {
    try {
      const { name } = req.params;
      const deleted = await db.deleteByName(STORAGE_KEYS.COLLECTIONS, decodeURIComponent(name));
      
      if (!deleted) {
        return errorResponse('Collection not found', 404);
      }

      return successResponse({ message: 'Collection deleted successfully' });
    } catch (error) {
      return errorResponse('Failed to delete collection: ' + error.message, 500);
    }
  });

  // 替换所有集合
  router.put('/api/collections', async (req) => {
    try {
      const data = await req.json();
      
      if (!Array.isArray(data)) {
        return errorResponse('Data must be an array', 400);
      }

      const collections = data.map(col => ({
        ...col,
        updatedAt: new Date().toISOString()
      }));

      const result = await db.replaceAll(STORAGE_KEYS.COLLECTIONS, collections);
      return successResponse(result);
    } catch (error) {
      return errorResponse('Failed to replace collections: ' + error.message, 500);
    }
  });
}
