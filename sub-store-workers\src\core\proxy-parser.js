/**
 * 代理解析器
 * 支持多种代理协议的解析
 */

import { Base64 } from 'js-base64';

// 工具函数
function isValidPort(port) {
  const p = parseInt(port);
  return p >= 1 && p <= 65535;
}

function isValidUUID(uuid) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// SS解析器
export function parseSSURI(uri) {
  try {
    const url = new URL(uri);
    let userInfo = url.username;
    
    // 处理base64编码的用户信息
    if (!url.password && userInfo.includes('@')) {
      const decoded = Base64.decode(userInfo);
      const [method, password] = decoded.split(':');
      userInfo = method;
      url.password = password;
    }

    const proxy = {
      type: 'ss',
      name: decodeURIComponent(url.hash.substring(1)) || `${url.hostname}:${url.port}`,
      server: url.hostname,
      port: parseInt(url.port),
      cipher: userInfo,
      password: url.password
    };

    // 解析插件参数
    const plugin = url.searchParams.get('plugin');
    if (plugin) {
      if (plugin.startsWith('obfs-local')) {
        const obfsParams = plugin.split(';');
        proxy.plugin = 'obfs';
        proxy['plugin-opts'] = {};
        obfsParams.forEach(param => {
          const [key, value] = param.split('=');
          if (key === 'obfs') proxy['plugin-opts'].mode = value;
          if (key === 'obfs-host') proxy['plugin-opts'].host = value;
        });
      }
    }

    return proxy;
  } catch (error) {
    console.error('SS URI parse error:', error);
    return null;
  }
}

// SSR解析器
export function parseSSRURI(uri) {
  try {
    const base64Part = uri.replace('ssr://', '');
    const decoded = Base64.decode(base64Part);
    const parts = decoded.split(':');
    
    if (parts.length < 6) return null;

    const [server, port, protocol, method, obfs, passwordBase64] = parts;
    const remaining = passwordBase64.split('/?');
    const password = Base64.decode(remaining[0]);
    
    const proxy = {
      type: 'ssr',
      name: server + ':' + port,
      server,
      port: parseInt(port),
      cipher: method,
      password,
      protocol,
      obfs
    };

    // 解析查询参数
    if (remaining[1]) {
      const params = new URLSearchParams(remaining[1]);
      if (params.get('remarks')) {
        proxy.name = Base64.decode(params.get('remarks'));
      }
      if (params.get('protoparam')) {
        proxy['protocol-param'] = Base64.decode(params.get('protoparam'));
      }
      if (params.get('obfsparam')) {
        proxy['obfs-param'] = Base64.decode(params.get('obfsparam'));
      }
    }

    return proxy;
  } catch (error) {
    console.error('SSR URI parse error:', error);
    return null;
  }
}

// VMess解析器
export function parseVMessURI(uri) {
  try {
    const base64Part = uri.replace('vmess://', '');
    const decoded = Base64.decode(base64Part);
    const config = JSON.parse(decoded);

    const proxy = {
      type: 'vmess',
      name: config.ps || `${config.add}:${config.port}`,
      server: config.add,
      port: parseInt(config.port),
      uuid: config.id,
      alterId: parseInt(config.aid) || 0,
      cipher: config.scy || 'auto'
    };

    // 网络类型
    if (config.net) {
      proxy.network = config.net;
      
      if (config.net === 'ws') {
        proxy['ws-opts'] = {};
        if (config.path) proxy['ws-opts'].path = config.path;
        if (config.host) proxy['ws-opts'].headers = { Host: config.host };
      } else if (config.net === 'h2') {
        proxy['h2-opts'] = {};
        if (config.path) proxy['h2-opts'].path = config.path;
        if (config.host) proxy['h2-opts'].host = [config.host];
      } else if (config.net === 'grpc') {
        proxy['grpc-opts'] = {};
        if (config.path) proxy['grpc-opts']['grpc-service-name'] = config.path;
      }
    }

    // TLS设置
    if (config.tls === 'tls') {
      proxy.tls = true;
      if (config.sni) proxy.sni = config.sni;
    }

    return proxy;
  } catch (error) {
    console.error('VMess URI parse error:', error);
    return null;
  }
}

// VLESS解析器
export function parseVLESSURI(uri) {
  try {
    const url = new URL(uri);

    const proxy = {
      type: 'vless',
      name: decodeURIComponent(url.hash.substring(1)) || `${url.hostname}:${url.port}`,
      server: url.hostname,
      port: parseInt(url.port),
      uuid: url.username
    };

    // 解析查询参数
    const params = url.searchParams;

    if (params.get('type')) {
      proxy.network = params.get('type');

      if (proxy.network === 'ws') {
        proxy['ws-opts'] = {};
        if (params.get('path')) proxy['ws-opts'].path = params.get('path');
        if (params.get('host')) proxy['ws-opts'].headers = { Host: params.get('host') };
      } else if (proxy.network === 'grpc') {
        proxy['grpc-opts'] = {};
        if (params.get('serviceName')) {
          proxy['grpc-opts']['grpc-service-name'] = params.get('serviceName');
        }
      }
    }

    // TLS设置
    if (params.get('security') === 'tls') {
      proxy.tls = true;
      if (params.get('sni')) proxy.sni = params.get('sni');
    }

    return proxy;
  } catch (error) {
    console.error('VLESS URI parse error:', error);
    return null;
  }
}

// Trojan解析器
export function parseTrojanURI(uri) {
  try {
    const url = new URL(uri);

    const proxy = {
      type: 'trojan',
      name: decodeURIComponent(url.hash.substring(1)) || `${url.hostname}:${url.port}`,
      server: url.hostname,
      port: parseInt(url.port),
      password: url.username
    };

    // 解析查询参数
    const params = url.searchParams;

    if (params.get('type')) {
      proxy.network = params.get('type');

      if (proxy.network === 'ws') {
        proxy['ws-opts'] = {};
        if (params.get('path')) proxy['ws-opts'].path = params.get('path');
        if (params.get('host')) proxy['ws-opts'].headers = { Host: params.get('host') };
      } else if (proxy.network === 'grpc') {
        proxy['grpc-opts'] = {};
        if (params.get('serviceName')) {
          proxy['grpc-opts']['grpc-service-name'] = params.get('serviceName');
        }
      }
    }

    // TLS默认开启
    proxy.tls = true;
    if (params.get('sni')) proxy.sni = params.get('sni');

    return proxy;
  } catch (error) {
    console.error('Trojan URI parse error:', error);
    return null;
  }
}

// 主解析函数
export function parseProxy(uri) {
  if (!uri || typeof uri !== 'string') return null;

  uri = uri.trim();

  if (uri.startsWith('ss://')) {
    return parseSSURI(uri);
  } else if (uri.startsWith('ssr://')) {
    return parseSSRURI(uri);
  } else if (uri.startsWith('vmess://')) {
    return parseVMessURI(uri);
  } else if (uri.startsWith('vless://')) {
    return parseVLESSURI(uri);
  } else if (uri.startsWith('trojan://')) {
    return parseTrojanURI(uri);
  }

  return null;
}

// 批量解析
export function parseProxies(content) {
  const proxies = [];
  const lines = content.split('\n');

  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const proxy = parseProxy(trimmed);
      if (proxy) {
        proxies.push(proxy);
      }
    }
  }

  return proxies;
}
