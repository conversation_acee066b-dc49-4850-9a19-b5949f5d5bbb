/**
 * 工件管理API路由
 */

import { Storage, Database, STORAGE_KEYS } from '../core/storage.js';
import { successResponse, errorResponse } from '../utils/response.js';

export default function artifactRoutes(router, env) {
  const storage = new Storage(env.SUB_STORE_KV);
  const db = new Database(storage);

  // 获取所有工件
  router.get('/api/artifacts', async (req) => {
    try {
      const artifacts = await db.getAll(STORAGE_KEYS.ARTIFACTS);
      return successResponse(artifacts);
    } catch (error) {
      return errorResponse('Failed to get artifacts: ' + error.message, 500);
    }
  });

  // 创建工件
  router.post('/api/artifacts', async (req) => {
    try {
      const data = await req.json();
      
      if (!data.name || !data.type || !data.source) {
        return errorResponse('Name, type and source are required', 400);
      }

      const artifact = {
        name: data.name,
        type: data.type, // 'subscription' or 'collection'
        source: data.source,
        platform: data.platform || 'clash',
        sync: data.sync || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const created = await db.create(STORAGE_KEYS.ARTIFACTS, data.name, artifact);
      return successResponse(created, 201);
    } catch (error) {
      return errorResponse('Failed to create artifact: ' + error.message, 500);
    }
  });

  // 获取单个工件
  router.get('/api/artifact/:name', async (req) => {
    try {
      const { name } = req.params;
      const artifact = await db.findByName(STORAGE_KEYS.ARTIFACTS, decodeURIComponent(name));
      
      if (!artifact) {
        return errorResponse('Artifact not found', 404);
      }

      return successResponse(artifact);
    } catch (error) {
      return errorResponse('Failed to get artifact: ' + error.message, 500);
    }
  });

  // 更新工件
  router.patch('/api/artifact/:name', async (req) => {
    try {
      const { name } = req.params;
      const data = await req.json();
      
      const existing = await db.findByName(STORAGE_KEYS.ARTIFACTS, decodeURIComponent(name));
      if (!existing) {
        return errorResponse('Artifact not found', 404);
      }

      const updated = {
        ...data,
        updatedAt: new Date().toISOString()
      };

      const result = await db.updateByName(STORAGE_KEYS.ARTIFACTS, decodeURIComponent(name), updated);
      return successResponse(result);
    } catch (error) {
      return errorResponse('Failed to update artifact: ' + error.message, 500);
    }
  });

  // 删除工件
  router.delete('/api/artifact/:name', async (req) => {
    try {
      const { name } = req.params;
      const deleted = await db.deleteByName(STORAGE_KEYS.ARTIFACTS, decodeURIComponent(name));
      
      if (!deleted) {
        return errorResponse('Artifact not found', 404);
      }

      return successResponse({ message: 'Artifact deleted successfully' });
    } catch (error) {
      return errorResponse('Failed to delete artifact: ' + error.message, 500);
    }
  });
}
