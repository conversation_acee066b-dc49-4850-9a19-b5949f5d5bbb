/**
 * 设置管理API路由
 */

import { Storage, Database, STORAGE_KEYS } from '../core/storage.js';
import { successResponse, errorResponse } from '../utils/response.js';

export default function settingsRoutes(router, env) {
  const storage = new Storage(env.SUB_STORE_KV);

  // 获取设置
  router.get('/api/settings', async (req) => {
    try {
      const settings = await storage.read(STORAGE_KEYS.SETTINGS) || getDefaultSettings();
      return successResponse(settings);
    } catch (error) {
      return errorResponse('Failed to get settings: ' + error.message, 500);
    }
  });

  // 更新设置
  router.patch('/api/settings', async (req) => {
    try {
      const data = await req.json();
      
      const currentSettings = await storage.read(STORAGE_KEYS.SETTINGS) || getDefaultSettings();
      const updatedSettings = {
        ...currentSettings,
        ...data,
        updatedAt: new Date().toISOString()
      };

      await storage.write(STORAGE_KEYS.SETTINGS, updatedSettings);
      return successResponse(updatedSettings);
    } catch (error) {
      return errorResponse('Failed to update settings: ' + error.message, 500);
    }
  });

  // 重置设置
  router.post('/api/settings/reset', async (req) => {
    try {
      const defaultSettings = getDefaultSettings();
      await storage.write(STORAGE_KEYS.SETTINGS, defaultSettings);
      return successResponse(defaultSettings);
    } catch (error) {
      return errorResponse('Failed to reset settings: ' + error.message, 500);
    }
  });
}

// 默认设置
function getDefaultSettings() {
  return {
    userAgent: 'Sub-Store/2.0.0',
    timeout: 10000,
    retries: 3,
    concurrency: 5,
    cacheExpiration: 3600000, // 1小时
    enableCache: true,
    enableLog: true,
    logLevel: 'info',
    defaultTarget: 'clash',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}
