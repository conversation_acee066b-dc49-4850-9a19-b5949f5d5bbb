/**
 * 代理处理器
 * 实现各种过滤和处理功能
 */

// 正则过滤器
export class RegexFilter {
  constructor(pattern, flags = 'i') {
    this.regex = new RegExp(pattern, flags);
  }

  apply(proxies) {
    return proxies.filter(proxy => this.regex.test(proxy.name));
  }
}

// 正则排除过滤器
export class RegexDiscardFilter {
  constructor(pattern, flags = 'i') {
    this.regex = new RegExp(pattern, flags);
  }

  apply(proxies) {
    return proxies.filter(proxy => !this.regex.test(proxy.name));
  }
}

// 地区过滤器
export class RegionFilter {
  constructor(regions, mode = 'include') {
    this.regions = Array.isArray(regions) ? regions : [regions];
    this.mode = mode; // 'include' or 'exclude'
  }

  apply(proxies) {
    const regionKeywords = {
      'HK': ['香港', 'HK', 'Hong Kong', 'HongKong'],
      'TW': ['台湾', 'TW', 'Taiwan', '台北', '台中'],
      'SG': ['新加坡', 'SG', 'Singapore', '狮城'],
      'JP': ['日本', 'JP', 'Japan', '东京', '大阪'],
      'US': ['美国', 'US', 'USA', 'America', '洛杉矶', '纽约'],
      'UK': ['英国', 'UK', 'Britain', 'London'],
      'KR': ['韩国', 'KR', 'Korea', '首尔'],
      'DE': ['德国', 'DE', 'Germany', '法兰克福'],
      'CA': ['加拿大', 'CA', 'Canada'],
      'AU': ['澳洲', 'AU', 'Australia', '悉尼'],
      'RU': ['俄罗斯', 'RU', 'Russia', '莫斯科'],
      'IN': ['印度', 'IN', 'India'],
      'TR': ['土耳其', 'TR', 'Turkey'],
      'NL': ['荷兰', 'NL', 'Netherlands', '阿姆斯特丹'],
      'FR': ['法国', 'FR', 'France', '巴黎']
    };

    return proxies.filter(proxy => {
      const hasRegion = this.regions.some(region => {
        const keywords = regionKeywords[region.toUpperCase()] || [region];
        return keywords.some(keyword => 
          proxy.name.toLowerCase().includes(keyword.toLowerCase())
        );
      });

      return this.mode === 'include' ? hasRegion : !hasRegion;
    });
  }
}

// 类型过滤器
export class TypeFilter {
  constructor(types, mode = 'include') {
    this.types = Array.isArray(types) ? types : [types];
    this.mode = mode; // 'include' or 'exclude'
  }

  apply(proxies) {
    return proxies.filter(proxy => {
      const hasType = this.types.includes(proxy.type);
      return this.mode === 'include' ? hasType : !hasType;
    });
  }
}

// 无用代理过滤器
export class UselessFilter {
  apply(proxies) {
    const uselessKeywords = [
      '过期', 'expire', 'expired',
      '流量', 'traffic', 'bandwidth',
      '官网', 'website', 'site',
      '群组', 'group', 'telegram',
      '测试', 'test', 'speed',
      '剩余', 'remaining', 'left'
    ];

    return proxies.filter(proxy => {
      return !uselessKeywords.some(keyword => 
        proxy.name.toLowerCase().includes(keyword.toLowerCase())
      );
    });
  }
}

// 排序处理器
export class SortProcessor {
  constructor(order = 'asc') {
    this.order = order; // 'asc' or 'desc'
  }

  apply(proxies) {
    return [...proxies].sort((a, b) => {
      const comparison = a.name.localeCompare(b.name);
      return this.order === 'asc' ? comparison : -comparison;
    });
  }
}

// 正则排序处理器
export class RegexSortProcessor {
  constructor(patterns, order = 'asc') {
    this.patterns = Array.isArray(patterns) ? patterns : [patterns];
    this.order = order;
  }

  apply(proxies) {
    return [...proxies].sort((a, b) => {
      const aIndex = this.getPatternIndex(a.name);
      const bIndex = this.getPatternIndex(b.name);
      
      if (aIndex !== bIndex) {
        const comparison = aIndex - bIndex;
        return this.order === 'asc' ? comparison : -comparison;
      }
      
      // 如果模式相同，按名称排序
      const nameComparison = a.name.localeCompare(b.name);
      return this.order === 'asc' ? nameComparison : -nameComparison;
    });
  }

  getPatternIndex(name) {
    for (let i = 0; i < this.patterns.length; i++) {
      if (new RegExp(this.patterns[i], 'i').test(name)) {
        return i;
      }
    }
    return this.patterns.length; // 未匹配的放在最后
  }
}

// 重命名处理器
export class RenameProcessor {
  constructor(pattern, replacement) {
    this.regex = new RegExp(pattern, 'g');
    this.replacement = replacement;
  }

  apply(proxies) {
    return proxies.map(proxy => ({
      ...proxy,
      name: proxy.name.replace(this.regex, this.replacement)
    }));
  }
}

// 删除处理器
export class DeleteProcessor {
  constructor(pattern) {
    this.regex = new RegExp(pattern, 'i');
  }

  apply(proxies) {
    return proxies.filter(proxy => !this.regex.test(proxy.name));
  }
}

// 属性设置处理器
export class PropertyProcessor {
  constructor(properties) {
    this.properties = properties;
  }

  apply(proxies) {
    return proxies.map(proxy => ({
      ...proxy,
      ...this.properties
    }));
  }
}

// 脚本处理器
export class ScriptProcessor {
  constructor(script) {
    this.script = script;
  }

  apply(proxies) {
    try {
      // 创建安全的执行环境
      const context = {
        proxies: JSON.parse(JSON.stringify(proxies)),
        console: {
          log: (...args) => console.log('[Script]', ...args)
        }
      };

      // 执行脚本
      const func = new Function('context', `
        with(context) {
          ${this.script}
          return proxies;
        }
      `);

      const result = func(context);
      return Array.isArray(result) ? result : proxies;
    } catch (error) {
      console.error('Script processor error:', error);
      return proxies;
    }
  }
}

// 主处理器应用函数
export async function applyProcessors(proxies, processors) {
  let result = [...proxies];

  for (const processor of processors) {
    try {
      const processorInstance = createProcessor(processor);
      if (processorInstance) {
        result = processorInstance.apply(result);
      }
    } catch (error) {
      console.error('Processor error:', error);
    }
  }

  return result;
}

// 创建处理器实例
function createProcessor(config) {
  const { type, ...options } = config;

  switch (type) {
    case 'Regex Filter':
      return new RegexFilter(options.pattern, options.flags);
    
    case 'Discard Regex Filter':
      return new RegexDiscardFilter(options.pattern, options.flags);
    
    case 'Region Filter':
      return new RegionFilter(options.regions, options.mode);
    
    case 'Type Filter':
      return new TypeFilter(options.types, options.mode);
    
    case 'Useless Filter':
      return new UselessFilter();
    
    case 'Sort':
      return new SortProcessor(options.order);
    
    case 'Regex Sort':
      return new RegexSortProcessor(options.patterns, options.order);
    
    case 'Rename':
      return new RenameProcessor(options.pattern, options.replacement);
    
    case 'Delete':
      return new DeleteProcessor(options.pattern);
    
    case 'Set Property':
      return new PropertyProcessor(options.properties);
    
    case 'Script':
      return new ScriptProcessor(options.script);
    
    default:
      console.warn(`Unknown processor type: ${type}`);
      return null;
  }
}
