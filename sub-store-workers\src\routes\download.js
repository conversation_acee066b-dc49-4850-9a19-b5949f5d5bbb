/**
 * 下载API路由
 * 处理订阅和集合的下载转换
 */

import { Storage, Database, STORAGE_KEYS } from '../core/storage.js';
import { parseProxies } from '../core/proxy-parser.js';
import { convertSubscription } from '../core/converter.js';
import { textResponse, errorResponse } from '../utils/response.js';
import { applyProcessors } from '../core/processors.js';

export default function downloadRoutes(router, env) {
  const storage = new Storage(env.SUB_STORE_KV);
  const db = new Database(storage);

  // 下载订阅
  router.get('/download/sub/:name', async (req) => {
    try {
      const { name } = req.params;
      const { target = 'clash', format } = req.query;
      
      const subscription = await db.findByName(STORAGE_KEYS.SUBSCRIPTIONS, decodeURIComponent(name));
      if (!subscription) {
        return errorResponse('Subscription not found', 404);
      }

      // 获取订阅内容
      const content = await fetchSubscriptionContent(subscription);
      if (!content) {
        return errorResponse('Failed to fetch subscription content', 500);
      }

      // 解析代理
      let proxies = parseProxies(content);
      if (proxies.length === 0) {
        return errorResponse('No valid proxies found', 400);
      }

      // 应用处理器
      if (subscription.process && subscription.process.length > 0) {
        proxies = await applyProcessors(proxies, subscription.process);
      }

      // 转换格式
      const outputFormat = format || target;
      const result = convertSubscription(proxies, outputFormat);
      
      const contentType = getContentType(outputFormat);
      return textResponse(result, 200, contentType);
    } catch (error) {
      return errorResponse('Download failed: ' + error.message, 500);
    }
  });

  // 下载集合
  router.get('/download/collection/:name', async (req) => {
    try {
      const { name } = req.params;
      const { target = 'clash', format } = req.query;
      
      const collection = await db.findByName(STORAGE_KEYS.COLLECTIONS, decodeURIComponent(name));
      if (!collection) {
        return errorResponse('Collection not found', 404);
      }

      // 获取所有订阅
      const allSubscriptions = await db.getAll(STORAGE_KEYS.SUBSCRIPTIONS);
      const subscriptionMap = {};
      allSubscriptions.forEach(sub => {
        subscriptionMap[sub.name] = sub;
      });

      // 收集所有代理
      let allProxies = [];
      
      for (const subName of collection.subscriptions) {
        const subscription = subscriptionMap[subName];
        if (subscription) {
          try {
            const content = await fetchSubscriptionContent(subscription);
            if (content) {
              let proxies = parseProxies(content);
              
              // 应用订阅级别的处理器
              if (subscription.process && subscription.process.length > 0) {
                proxies = await applyProcessors(proxies, subscription.process);
              }
              
              allProxies = allProxies.concat(proxies);
            }
          } catch (error) {
            console.error(`Failed to process subscription ${subName}:`, error);
          }
        }
      }

      if (allProxies.length === 0) {
        return errorResponse('No valid proxies found in collection', 400);
      }

      // 应用集合级别的处理器
      if (collection.process && collection.process.length > 0) {
        allProxies = await applyProcessors(allProxies, collection.process);
      }

      // 转换格式
      const outputFormat = format || target;
      const result = convertSubscription(allProxies, outputFormat);
      
      const contentType = getContentType(outputFormat);
      return textResponse(result, 200, contentType);
    } catch (error) {
      return errorResponse('Download failed: ' + error.message, 500);
    }
  });

  // 通用下载路由
  router.get('/download/:type/:name', async (req) => {
    const { type } = req.params;
    
    if (type === 'sub') {
      return router.handle({
        ...req,
        path: req.path.replace('/download/sub/', '/download/sub/')
      });
    } else if (type === 'collection') {
      return router.handle({
        ...req,
        path: req.path.replace('/download/collection/', '/download/collection/')
      });
    }
    
    return errorResponse('Invalid download type', 400);
  });
}

// 获取订阅内容
async function fetchSubscriptionContent(subscription) {
  try {
    const headers = {
      'User-Agent': subscription.ua || 'Sub-Store/2.0.0'
    };

    const response = await fetch(subscription.url, { headers });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.text();
  } catch (error) {
    console.error('Failed to fetch subscription:', error);
    return null;
  }
}

// 获取内容类型
function getContentType(format) {
  switch (format.toLowerCase()) {
    case 'clash':
      return 'application/x-yaml';
    case 'surge':
      return 'text/plain';
    case 'quantumult-x':
    case 'qx':
      return 'text/plain';
    case 'uri':
      return 'text/plain';
    default:
      return 'text/plain';
  }
}
