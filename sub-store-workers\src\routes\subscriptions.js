/**
 * 订阅管理API路由
 */

import { Storage, Database, STORAGE_KEYS } from '../core/storage.js';
import { parseProxies } from '../core/proxy-parser.js';
import { successResponse, errorResponse } from '../utils/response.js';

export default function subscriptionRoutes(router, env) {
  const storage = new Storage(env.SUB_STORE_KV);
  const db = new Database(storage);

  // 获取所有订阅
  router.get('/api/subs', async (req) => {
    try {
      const subscriptions = await db.getAll(STORAGE_KEYS.SUBSCRIPTIONS);
      return successResponse(subscriptions);
    } catch (error) {
      return errorResponse('Failed to get subscriptions: ' + error.message, 500);
    }
  });

  // 创建订阅
  router.post('/api/subs', async (req) => {
    try {
      const data = await req.json();
      
      if (!data.name || !data.url) {
        return errorResponse('Name and URL are required', 400);
      }

      // 检查订阅是否已存在
      const existing = await db.findByName(STORAGE_KEYS.SUBSCRIPTIONS, data.name);
      if (existing) {
        return errorResponse('Subscription already exists', 409);
      }

      const subscription = {
        name: data.name,
        url: data.url,
        tag: data.tag || '',
        ua: data.ua || '',
        process: data.process || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const created = await db.create(STORAGE_KEYS.SUBSCRIPTIONS, data.name, subscription);
      return successResponse(created, 201);
    } catch (error) {
      return errorResponse('Failed to create subscription: ' + error.message, 500);
    }
  });

  // 获取单个订阅
  router.get('/api/sub/:name', async (req) => {
    try {
      const { name } = req.params;
      const subscription = await db.findByName(STORAGE_KEYS.SUBSCRIPTIONS, decodeURIComponent(name));
      
      if (!subscription) {
        return errorResponse('Subscription not found', 404);
      }

      return successResponse(subscription);
    } catch (error) {
      return errorResponse('Failed to get subscription: ' + error.message, 500);
    }
  });

  // 更新订阅
  router.patch('/api/sub/:name', async (req) => {
    try {
      const { name } = req.params;
      const data = await req.json();
      
      const existing = await db.findByName(STORAGE_KEYS.SUBSCRIPTIONS, decodeURIComponent(name));
      if (!existing) {
        return errorResponse('Subscription not found', 404);
      }

      const updated = {
        ...data,
        updatedAt: new Date().toISOString()
      };

      const result = await db.updateByName(STORAGE_KEYS.SUBSCRIPTIONS, decodeURIComponent(name), updated);
      return successResponse(result);
    } catch (error) {
      return errorResponse('Failed to update subscription: ' + error.message, 500);
    }
  });

  // 删除订阅
  router.delete('/api/sub/:name', async (req) => {
    try {
      const { name } = req.params;
      const deleted = await db.deleteByName(STORAGE_KEYS.SUBSCRIPTIONS, decodeURIComponent(name));
      
      if (!deleted) {
        return errorResponse('Subscription not found', 404);
      }

      return successResponse({ message: 'Subscription deleted successfully' });
    } catch (error) {
      return errorResponse('Failed to delete subscription: ' + error.message, 500);
    }
  });

  // 替换所有订阅
  router.put('/api/subs', async (req) => {
    try {
      const data = await req.json();
      
      if (!Array.isArray(data)) {
        return errorResponse('Data must be an array', 400);
      }

      const subscriptions = data.map(sub => ({
        ...sub,
        updatedAt: new Date().toISOString()
      }));

      const result = await db.replaceAll(STORAGE_KEYS.SUBSCRIPTIONS, subscriptions);
      return successResponse(result);
    } catch (error) {
      return errorResponse('Failed to replace subscriptions: ' + error.message, 500);
    }
  });

  // 获取订阅流量信息
  router.get('/api/sub/flow/:name', async (req) => {
    try {
      const { name } = req.params;
      const { url } = req.query;
      
      let targetUrl = url;
      if (!targetUrl) {
        const subscription = await db.findByName(STORAGE_KEYS.SUBSCRIPTIONS, decodeURIComponent(name));
        if (!subscription) {
          return errorResponse('Subscription not found', 404);
        }
        targetUrl = subscription.url;
      }

      // 获取订阅内容和流量信息
      const response = await fetch(targetUrl, {
        headers: {
          'User-Agent': 'Sub-Store/2.0.0'
        }
      });

      if (!response.ok) {
        return errorResponse('Failed to fetch subscription', response.status);
      }

      // 解析流量信息
      const flowInfo = parseFlowHeaders(response.headers);
      
      return successResponse({
        name: decodeURIComponent(name),
        url: targetUrl,
        ...flowInfo,
        lastUpdate: new Date().toISOString()
      });
    } catch (error) {
      return errorResponse('Failed to get flow info: ' + error.message, 500);
    }
  });
}

// 解析流量信息
function parseFlowHeaders(headers) {
  const flowInfo = {
    upload: 0,
    download: 0,
    total: 0,
    expire: null
  };

  // 解析 subscription-userinfo 头
  const userInfo = headers.get('subscription-userinfo');
  if (userInfo) {
    const parts = userInfo.split(';');
    parts.forEach(part => {
      const [key, value] = part.trim().split('=');
      if (key && value) {
        switch (key.toLowerCase()) {
          case 'upload':
            flowInfo.upload = parseInt(value) || 0;
            break;
          case 'download':
            flowInfo.download = parseInt(value) || 0;
            break;
          case 'total':
            flowInfo.total = parseInt(value) || 0;
            break;
          case 'expire':
            flowInfo.expire = parseInt(value) || null;
            break;
        }
      }
    });
  }

  // 计算使用量和剩余量
  flowInfo.used = flowInfo.upload + flowInfo.download;
  flowInfo.remaining = Math.max(0, flowInfo.total - flowInfo.used);
  
  // 计算剩余天数
  if (flowInfo.expire) {
    const expireDate = new Date(flowInfo.expire * 1000);
    const now = new Date();
    flowInfo.remainingDays = Math.max(0, Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24)));
  }

  return flowInfo;
}
