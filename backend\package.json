{"name": "sub-store", "version": "2.19.92", "description": "Advanced Subscription Manager for QX, Loon, Surge, Stash and Shadowrocket.", "main": "src/main.js", "scripts": {"preinstall": "npx only-allow pnpm", "test": "gulp peggy && npx cross-env BABEL_ENV=test mocha src/test/**/*.spec.js --require @babel/register --recursive", "serve": "node sub-store.min.js", "start": "nodemon -w src -w package.json --exec babel-node src/main.js", "dev:esbuild": "nodemon -w src -w package.json dev-esbuild.js", "dev:run": "nodemon -w sub-store.min.js sub-store.min.js", "build": "gulp", "bundle": "node bundle.js", "bundle:esbuild": "node bundle-esbuild.js", "changelog": "conventional-changelog -p cli -i CHANGELOG.md -s"}, "author": "Peng-YM", "license": "GPL-3.0", "pnpm": {"patchedDependencies": {"http-proxy@1.18.1": "patches/<EMAIL>"}}, "dependencies": {"@maxmind/geoip2-node": "^5.0.0", "automerge": "1.0.1-preview.7", "body-parser": "^1.19.0", "buffer": "^6.0.3", "connect-history-api-fallback": "^2.0.0", "cron": "^3.1.6", "dns-packet": "^5.6.1", "dotenv": "^16.4.7", "express": "^4.17.1", "fetch-socks": "^1.3.2", "http-proxy-middleware": "^3.0.3", "ip-address": "^9.0.5", "js-base64": "^3.7.2", "json5": "^2.2.3", "jsrsasign": "^11.1.0", "lodash": "^4.17.21", "mime-types": "^2.1.35", "ms": "^2.1.3", "nanoid": "^3.3.3", "semver": "^7.6.3", "static-js-yaml": "^1.0.0", "undici": "^7.4.0"}, "devDependencies": {"@babel/core": "^7.18.0", "@babel/node": "^7.17.10", "@babel/preset-env": "^7.18.0", "@babel/register": "^7.17.7", "@types/gulp": "^4.0.9", "axios": "^0.21.2", "babel-plugin-relative-path-import": "^2.0.1", "babelify": "^10.0.0", "browser-pack-flat": "^3.4.2", "browserify": "^17.0.0", "chai": "^4.3.6", "esbuild": "^0.19.8", "eslint": "^8.16.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-eslint-new": "^1.4.4", "gulp-file": "^0.4.0", "gulp-header": "^2.0.9", "gulp-prettier": "^4.0.0", "gulp-tap": "^2.0.0", "mocha": "^10.0.0", "nodemon": "^2.0.16", "peggy": "^2.0.1", "prettier": "2.6.2", "prettier-plugin-sort-imports": "^1.6.1", "tinyify": "^3.0.0"}}