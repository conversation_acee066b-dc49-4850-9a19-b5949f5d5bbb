name = "sub-store-workers"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# KV命名空间配置
[[kv_namespaces]]
binding = "SUB_STORE_KV"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"

# 环境变量配置
[vars]
ENVIRONMENT = "production"
VERSION = "2.0.0"
CORS_ORIGIN = "*"

# 开发环境配置
[env.dev]
name = "sub-store-workers-dev"
vars = { ENVIRONMENT = "development" }

# 生产环境配置  
[env.production]
name = "sub-store-workers-prod"
vars = { ENVIRONMENT = "production" }
