/**
 * 下载工具函数
 */

export async function fetchWithTimeout(url, options = {}, timeout = 10000) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

export async function downloadSubscription(url, userAgent = 'Sub-Store/2.0.0') {
  try {
    const response = await fetchWithTimeout(url, {
      headers: {
        'User-Agent': userAgent
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.text();
  } catch (error) {
    console.error('Download subscription failed:', error);
    throw error;
  }
}
