/**
 * Sub-Store for Cloudflare Workers
 * Advanced Subscription Manager
 * 
 * <AUTHOR> Workers Team
 * @version 2.0.0
 */

import { Router } from './core/router.js';
import { corsHeaders, handleCORS } from './utils/cors.js';
import { errorResponse, successResponse } from './utils/response.js';

// 导入路由处理器
import subscriptionRoutes from './routes/subscriptions.js';
import collectionRoutes from './routes/collections.js';
import artifactRoutes from './routes/artifacts.js';
import settingsRoutes from './routes/settings.js';
import downloadRoutes from './routes/download.js';

export default {
  async fetch(request, env, ctx) {
    try {
      // 处理CORS预检请求
      if (request.method === 'OPTIONS') {
        return handleCORS();
      }

      // 创建路由器实例
      const router = new Router();
      
      // 注册路由
      subscriptionRoutes(router, env);
      collectionRoutes(router, env);
      artifactRoutes(router, env);
      settingsRoutes(router, env);
      downloadRoutes(router, env);

      // 健康检查端点
      router.get('/health', () => {
        return successResponse({
          status: 'ok',
          version: env.VERSION || '2.0.0',
          environment: env.ENVIRONMENT || 'production',
          timestamp: new Date().toISOString()
        });
      });

      // API信息端点
      router.get('/api/info', () => {
        return successResponse({
          name: 'Sub-Store Workers',
          version: env.VERSION || '2.0.0',
          description: 'Advanced Subscription Manager for Cloudflare Workers',
          author: 'Sub-Store Workers Team',
          endpoints: [
            'GET /health',
            'GET /api/info',
            'GET /api/subs',
            'POST /api/subs',
            'GET /api/sub/:name',
            'PATCH /api/sub/:name',
            'DELETE /api/sub/:name',
            'GET /api/collections',
            'POST /api/collections',
            'GET /api/collection/:name',
            'PATCH /api/collection/:name',
            'DELETE /api/collection/:name',
            'GET /download/:type/:name',
            'GET /api/settings',
            'PATCH /api/settings'
          ]
        });
      });

      // 处理请求
      const response = await router.handle(request);
      
      if (response) {
        // 添加CORS头
        Object.entries(corsHeaders).forEach(([key, value]) => {
          response.headers.set(key, value);
        });
        return response;
      }

      // 404处理
      return errorResponse('Not Found', 404);

    } catch (error) {
      console.error('Worker Error:', error);
      return errorResponse(
        'Internal Server Error: ' + error.message,
        500
      );
    }
  }
};
