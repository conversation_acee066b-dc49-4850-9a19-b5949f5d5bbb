/**
 * 简单的路由器实现
 * 适用于Cloudflare Workers环境
 */

export class Router {
  constructor() {
    this.routes = [];
  }

  // 添加路由
  addRoute(method, path, handler) {
    this.routes.push({
      method: method.toUpperCase(),
      path,
      handler,
      regex: this.pathToRegex(path)
    });
  }

  // HTTP方法快捷方式
  get(path, handler) {
    this.addRoute('GET', path, handler);
  }

  post(path, handler) {
    this.addRoute('POST', path, handler);
  }

  put(path, handler) {
    this.addRoute('PUT', path, handler);
  }

  patch(path, handler) {
    this.addRoute('PATCH', path, handler);
  }

  delete(path, handler) {
    this.addRoute('DELETE', path, handler);
  }

  // 将路径转换为正则表达式
  pathToRegex(path) {
    // 处理参数路径，如 /api/sub/:name
    const regexPath = path
      .replace(/\//g, '\\/')
      .replace(/:([^\/]+)/g, '([^\/]+)');
    return new RegExp(`^${regexPath}$`);
  }

  // 提取路径参数
  extractParams(path, regex, originalPath) {
    const match = path.match(regex);
    if (!match) return {};

    const params = {};
    const paramNames = originalPath.match(/:([^\/]+)/g);
    
    if (paramNames) {
      paramNames.forEach((paramName, index) => {
        const name = paramName.substring(1); // 移除冒号
        params[name] = match[index + 1];
      });
    }

    return params;
  }

  // 解析查询参数
  parseQuery(url) {
    const query = {};
    const queryString = url.split('?')[1];
    
    if (queryString) {
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key) {
          query[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
        }
      });
    }

    return query;
  }

  // 处理请求
  async handle(request) {
    const url = new URL(request.url);
    const method = request.method.toUpperCase();
    const path = url.pathname;

    // 查找匹配的路由
    for (const route of this.routes) {
      if (route.method === method && route.regex.test(path)) {
        try {
          // 提取参数
          const params = this.extractParams(path, route.regex, route.path);
          const query = this.parseQuery(request.url);

          // 构建请求对象
          const req = {
            method,
            url: request.url,
            path,
            params,
            query,
            headers: request.headers,
            body: request.body,
            json: async () => {
              try {
                return await request.json();
              } catch {
                return null;
              }
            },
            text: async () => {
              try {
                return await request.text();
              } catch {
                return '';
              }
            }
          };

          // 调用处理器
          return await route.handler(req);
        } catch (error) {
          console.error(`Route handler error for ${method} ${path}:`, error);
          throw error;
        }
      }
    }

    return null; // 未找到匹配的路由
  }
}
