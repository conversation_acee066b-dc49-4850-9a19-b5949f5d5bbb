/**
 * 响应处理工具
 */

export function successResponse(data, status = 200) {
  return new Response(JSON.stringify({
    success: true,
    data
  }), {
    status,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export function errorResponse(message, status = 400, code = null) {
  return new Response(JSON.stringify({
    success: false,
    error: {
      message,
      code,
      status
    }
  }), {
    status,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

export function textResponse(text, status = 200, contentType = 'text/plain') {
  return new Response(text, {
    status,
    headers: {
      'Content-Type': contentType
    }
  });
}
