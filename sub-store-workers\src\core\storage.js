/**
 * KV存储管理器
 * 提供统一的数据存储接口
 */

export class Storage {
  constructor(kv) {
    this.kv = kv;
  }

  // 读取数据
  async read(key) {
    try {
      const value = await this.kv.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Storage read error for key ${key}:`, error);
      return null;
    }
  }

  // 写入数据
  async write(key, value) {
    try {
      await this.kv.put(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Storage write error for key ${key}:`, error);
      return false;
    }
  }

  // 删除数据
  async delete(key) {
    try {
      await this.kv.delete(key);
      return true;
    } catch (error) {
      console.error(`Storage delete error for key ${key}:`, error);
      return false;
    }
  }

  // 列出所有键
  async list(prefix = '') {
    try {
      const result = await this.kv.list({ prefix });
      return result.keys.map(key => key.name);
    } catch (error) {
      console.error(`Storage list error:`, error);
      return [];
    }
  }

  // 检查键是否存在
  async exists(key) {
    try {
      const value = await this.kv.get(key);
      return value !== null;
    } catch (error) {
      console.error(`Storage exists error for key ${key}:`, error);
      return false;
    }
  }
}

// 数据库操作工具函数
export class Database {
  constructor(storage) {
    this.storage = storage;
  }

  // 查找项目
  async findByName(collection, name) {
    const data = await this.storage.read(collection) || {};
    return data[name] || null;
  }

  // 更新项目
  async updateByName(collection, name, item) {
    const data = await this.storage.read(collection) || {};
    data[name] = { ...data[name], ...item, name };
    await this.storage.write(collection, data);
    return data[name];
  }

  // 删除项目
  async deleteByName(collection, name) {
    const data = await this.storage.read(collection) || {};
    const deleted = data[name];
    delete data[name];
    await this.storage.write(collection, data);
    return deleted;
  }

  // 获取所有项目
  async getAll(collection) {
    const data = await this.storage.read(collection) || {};
    return Object.values(data);
  }

  // 创建项目
  async create(collection, name, item) {
    const data = await this.storage.read(collection) || {};
    data[name] = { ...item, name, createdAt: new Date().toISOString() };
    await this.storage.write(collection, data);
    return data[name];
  }

  // 替换整个集合
  async replaceAll(collection, items) {
    const data = {};
    items.forEach(item => {
      data[item.name] = item;
    });
    await this.storage.write(collection, data);
    return Object.values(data);
  }
}

// 存储键常量
export const STORAGE_KEYS = {
  SUBSCRIPTIONS: 'subs',
  COLLECTIONS: 'collections', 
  SETTINGS: 'settings',
  ARTIFACTS: 'artifacts',
  FILES: 'files',
  TOKENS: 'tokens',
  MODULES: 'modules'
};
