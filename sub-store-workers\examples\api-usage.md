# API使用示例

本文档提供Sub-Store Workers API的详细使用示例。

## 基础配置

假设你的Workers部署在：`https://sub-store.your-domain.workers.dev`

## 订阅管理示例

### 1. 创建订阅

```bash
curl -X POST https://sub-store.your-domain.workers.dev/api/subs \
  -H "Content-Type: application/json" \
  -d '{
    "name": "我的订阅",
    "url": "https://example.com/subscription",
    "tag": "高速节点",
    "ua": "Clash/1.0.0",
    "process": [
      {
        "type": "Region Filter",
        "regions": ["HK", "TW", "SG"],
        "mode": "include"
      },
      {
        "type": "Sort",
        "order": "asc"
      }
    ]
  }'
```

### 2. 获取所有订阅

```bash
curl https://sub-store.your-domain.workers.dev/api/subs
```

### 3. 更新订阅

```bash
curl -X PATCH https://sub-store.your-domain.workers.dev/api/sub/我的订阅 \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://new-example.com/subscription",
    "process": [
      {
        "type": "Regex Filter",
        "pattern": "香港|HK|Taiwan|TW",
        "flags": "i"
      }
    ]
  }'
```

### 4. 获取订阅流量信息

```bash
curl https://sub-store.your-domain.workers.dev/api/sub/flow/我的订阅
```

## 集合管理示例

### 1. 创建集合

```bash
curl -X POST https://sub-store.your-domain.workers.dev/api/collections \
  -H "Content-Type: application/json" \
  -d '{
    "name": "全球节点",
    "subscriptions": ["我的订阅", "备用订阅"],
    "process": [
      {
        "type": "Useless Filter"
      },
      {
        "type": "Regex Sort",
        "patterns": ["香港", "台湾", "新加坡", "日本", "美国"],
        "order": "asc"
      }
    ]
  }'
```

### 2. 获取集合

```bash
curl https://sub-store.your-domain.workers.dev/api/collection/全球节点
```

## 下载转换示例

### 1. 下载Clash配置

```bash
curl "https://sub-store.your-domain.workers.dev/download/sub/我的订阅?target=clash" \
  -o clash-config.yaml
```

### 2. 下载Surge配置

```bash
curl "https://sub-store.your-domain.workers.dev/download/collection/全球节点?target=surge" \
  -o surge-config.conf
```

### 3. 下载QuantumultX配置

```bash
curl "https://sub-store.your-domain.workers.dev/download/sub/我的订阅?target=qx" \
  -o qx-config.conf
```

### 4. 下载URI格式

```bash
curl "https://sub-store.your-domain.workers.dev/download/sub/我的订阅?target=uri" \
  -o proxies.txt
```

## 处理器配置示例

### 1. 复杂过滤配置

```json
{
  "name": "高级过滤订阅",
  "url": "https://example.com/subscription",
  "process": [
    {
      "type": "Regex Filter",
      "pattern": "香港|HK|台湾|TW|新加坡|SG",
      "flags": "i"
    },
    {
      "type": "Discard Regex Filter", 
      "pattern": "过期|expire|测试|test",
      "flags": "i"
    },
    {
      "type": "Type Filter",
      "types": ["vmess", "trojan", "vless"],
      "mode": "include"
    },
    {
      "type": "Useless Filter"
    },
    {
      "type": "Rename",
      "pattern": "\\[.*?\\]",
      "replacement": ""
    },
    {
      "type": "Regex Sort",
      "patterns": ["香港", "台湾", "新加坡", "日本", "美国"],
      "order": "asc"
    }
  ]
}
```

### 2. 脚本处理器示例

```json
{
  "type": "Script",
  "script": `
    // 添加地区标识
    proxies.forEach(proxy => {
      if (proxy.name.includes('香港') || proxy.name.includes('HK')) {
        proxy.name = '🇭🇰 ' + proxy.name;
      } else if (proxy.name.includes('台湾') || proxy.name.includes('TW')) {
        proxy.name = '🇹🇼 ' + proxy.name;
      } else if (proxy.name.includes('新加坡') || proxy.name.includes('SG')) {
        proxy.name = '🇸🇬 ' + proxy.name;
      } else if (proxy.name.includes('日本') || proxy.name.includes('JP')) {
        proxy.name = '🇯🇵 ' + proxy.name;
      } else if (proxy.name.includes('美国') || proxy.name.includes('US')) {
        proxy.name = '🇺🇸 ' + proxy.name;
      }
    });
    
    // 设置UDP支持
    proxies.forEach(proxy => {
      if (proxy.type === 'vmess' || proxy.type === 'vless') {
        proxy.udp = true;
      }
    });
  `
}
```

## 设置管理示例

### 1. 获取当前设置

```bash
curl https://sub-store.your-domain.workers.dev/api/settings
```

### 2. 更新设置

```bash
curl -X PATCH https://sub-store.your-domain.workers.dev/api/settings \
  -H "Content-Type: application/json" \
  -d '{
    "userAgent": "Sub-Store-Workers/2.0.0",
    "timeout": 15000,
    "retries": 5,
    "enableCache": true,
    "cacheExpiration": 7200000
  }'
```

## JavaScript客户端示例

### 基础客户端类

```javascript
class SubStoreClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl.replace(/\/$/, '');
  }

  async request(method, path, data = null) {
    const url = `${this.baseUrl}${path}`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error.message);
    }

    return result.data;
  }

  // 订阅管理
  async getSubscriptions() {
    return this.request('GET', '/api/subs');
  }

  async createSubscription(subscription) {
    return this.request('POST', '/api/subs', subscription);
  }

  async updateSubscription(name, updates) {
    return this.request('PATCH', `/api/sub/${encodeURIComponent(name)}`, updates);
  }

  async deleteSubscription(name) {
    return this.request('DELETE', `/api/sub/${encodeURIComponent(name)}`);
  }

  // 集合管理
  async getCollections() {
    return this.request('GET', '/api/collections');
  }

  async createCollection(collection) {
    return this.request('POST', '/api/collections', collection);
  }

  // 下载链接生成
  getDownloadUrl(type, name, target = 'clash') {
    return `${this.baseUrl}/download/${type}/${encodeURIComponent(name)}?target=${target}`;
  }
}

// 使用示例
const client = new SubStoreClient('https://sub-store.your-domain.workers.dev');

// 创建订阅
await client.createSubscription({
  name: '测试订阅',
  url: 'https://example.com/sub',
  process: [
    { type: 'Region Filter', regions: ['HK', 'TW'], mode: 'include' }
  ]
});

// 获取下载链接
const clashUrl = client.getDownloadUrl('sub', '测试订阅', 'clash');
console.log('Clash配置链接:', clashUrl);
```

## 错误处理

### 常见错误响应

```json
{
  "success": false,
  "error": {
    "message": "Subscription not found",
    "code": "NOT_FOUND",
    "status": 404
  }
}
```

### 错误处理示例

```javascript
try {
  const subscription = await client.getSubscription('不存在的订阅');
} catch (error) {
  if (error.message.includes('not found')) {
    console.log('订阅不存在');
  } else {
    console.error('其他错误:', error.message);
  }
}
```

## 批量操作示例

### 批量创建订阅

```javascript
const subscriptions = [
  { name: '订阅1', url: 'https://example1.com/sub' },
  { name: '订阅2', url: 'https://example2.com/sub' },
  { name: '订阅3', url: 'https://example3.com/sub' }
];

for (const sub of subscriptions) {
  try {
    await client.createSubscription(sub);
    console.log(`创建订阅 ${sub.name} 成功`);
  } catch (error) {
    console.error(`创建订阅 ${sub.name} 失败:`, error.message);
  }
}
```

### 批量更新处理器

```javascript
const subscriptions = await client.getSubscriptions();

const commonProcessors = [
  { type: 'Useless Filter' },
  { type: 'Sort', order: 'asc' }
];

for (const sub of subscriptions) {
  await client.updateSubscription(sub.name, {
    process: [...(sub.process || []), ...commonProcessors]
  });
}
```
