/**
 * 订阅转换器
 * 支持多种输出格式
 */

import { Base64 } from 'js-base64';
import yaml from 'js-yaml';

// Clash格式转换器
export class ClashConverter {
  static convert(proxies, config = {}) {
    const clashConfig = {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      proxies: [],
      'proxy-groups': [],
      rules: []
    };

    // 转换代理
    clashConfig.proxies = proxies.map(proxy => this.convertProxy(proxy));

    // 添加代理组
    if (clashConfig.proxies.length > 0) {
      clashConfig['proxy-groups'].push({
        name: 'Proxy',
        type: 'select',
        proxies: ['DIRECT', ...clashConfig.proxies.map(p => p.name)]
      });

      clashConfig['proxy-groups'].push({
        name: 'Auto',
        type: 'url-test',
        proxies: clashConfig.proxies.map(p => p.name),
        url: 'http://www.gstatic.com/generate_204',
        interval: 300
      });
    }

    // 基础规则
    clashConfig.rules = [
      'DOMAIN-SUFFIX,local,DIRECT',
      'IP-CIDR,*********/8,DIRECT',
      'IP-CIDR,**********/12,DIRECT',
      'IP-CIDR,***********/16,DIRECT',
      'IP-CIDR,10.0.0.0/8,DIRECT',
      'IP-CIDR,********/8,DIRECT',
      'IP-CIDR,**********/10,DIRECT',
      'GEOIP,CN,DIRECT',
      'MATCH,Proxy'
    ];

    return yaml.dump(clashConfig);
  }

  static convertProxy(proxy) {
    const base = {
      name: proxy.name,
      type: proxy.type,
      server: proxy.server,
      port: proxy.port
    };

    switch (proxy.type) {
      case 'ss':
        return {
          ...base,
          cipher: proxy.cipher,
          password: proxy.password,
          ...(proxy.plugin && { plugin: proxy.plugin }),
          ...(proxy['plugin-opts'] && { 'plugin-opts': proxy['plugin-opts'] })
        };

      case 'ssr':
        return {
          ...base,
          cipher: proxy.cipher,
          password: proxy.password,
          protocol: proxy.protocol,
          obfs: proxy.obfs,
          ...(proxy['protocol-param'] && { 'protocol-param': proxy['protocol-param'] }),
          ...(proxy['obfs-param'] && { 'obfs-param': proxy['obfs-param'] })
        };

      case 'vmess':
        return {
          ...base,
          uuid: proxy.uuid,
          alterId: proxy.alterId || 0,
          cipher: proxy.cipher || 'auto',
          ...(proxy.network && { network: proxy.network }),
          ...(proxy.tls && { tls: proxy.tls }),
          ...(proxy.sni && { servername: proxy.sni }),
          ...(proxy['ws-opts'] && { 'ws-opts': proxy['ws-opts'] }),
          ...(proxy['h2-opts'] && { 'h2-opts': proxy['h2-opts'] }),
          ...(proxy['grpc-opts'] && { 'grpc-opts': proxy['grpc-opts'] })
        };

      case 'vless':
        return {
          ...base,
          uuid: proxy.uuid,
          ...(proxy.network && { network: proxy.network }),
          ...(proxy.tls && { tls: proxy.tls }),
          ...(proxy.sni && { servername: proxy.sni }),
          ...(proxy['ws-opts'] && { 'ws-opts': proxy['ws-opts'] }),
          ...(proxy['grpc-opts'] && { 'grpc-opts': proxy['grpc-opts'] })
        };

      case 'trojan':
        return {
          ...base,
          password: proxy.password,
          ...(proxy.network && { network: proxy.network }),
          ...(proxy.sni && { sni: proxy.sni }),
          ...(proxy['ws-opts'] && { 'ws-opts': proxy['ws-opts'] }),
          ...(proxy['grpc-opts'] && { 'grpc-opts': proxy['grpc-opts'] })
        };

      default:
        return base;
    }
  }
}

// Surge格式转换器
export class SurgeConverter {
  static convert(proxies, config = {}) {
    const lines = ['[General]'];
    lines.push('loglevel = notify');
    lines.push('bypass-system = true');
    lines.push('skip-proxy = 127.0.0.1, ***********/16, 10.0.0.0/8, **********/12, **********/10, localhost, *.local');
    lines.push('');

    lines.push('[Proxy]');
    proxies.forEach(proxy => {
      const surgeProxy = this.convertProxy(proxy);
      if (surgeProxy) {
        lines.push(surgeProxy);
      }
    });
    lines.push('');

    lines.push('[Proxy Group]');
    if (proxies.length > 0) {
      const proxyNames = proxies.map(p => p.name).join(', ');
      lines.push(`Proxy = select, ${proxyNames}`);
      lines.push(`Auto = url-test, ${proxyNames}, url = http://www.gstatic.com/generate_204, interval = 600`);
    }
    lines.push('');

    lines.push('[Rule]');
    lines.push('DOMAIN-SUFFIX,local,DIRECT');
    lines.push('IP-CIDR,*********/8,DIRECT');
    lines.push('IP-CIDR,**********/12,DIRECT');
    lines.push('IP-CIDR,***********/16,DIRECT');
    lines.push('IP-CIDR,10.0.0.0/8,DIRECT');
    lines.push('GEOIP,CN,DIRECT');
    lines.push('FINAL,Proxy,dns-failed');

    return lines.join('\n');
  }

  static convertProxy(proxy) {
    switch (proxy.type) {
      case 'ss':
        return `${proxy.name} = ss, ${proxy.server}, ${proxy.port}, encrypt-method=${proxy.cipher}, password=${proxy.password}`;
      
      case 'vmess':
        let vmessLine = `${proxy.name} = vmess, ${proxy.server}, ${proxy.port}, username=${proxy.uuid}`;
        if (proxy.tls) vmessLine += ', tls=true';
        if (proxy.network === 'ws') {
          vmessLine += ', ws=true';
          if (proxy['ws-opts']?.path) vmessLine += `, ws-path=${proxy['ws-opts'].path}`;
          if (proxy['ws-opts']?.headers?.Host) vmessLine += `, ws-headers=Host:${proxy['ws-opts'].headers.Host}`;
        }
        return vmessLine;
      
      case 'trojan':
        let trojanLine = `${proxy.name} = trojan, ${proxy.server}, ${proxy.port}, password=${proxy.password}`;
        if (proxy.sni) trojanLine += `, sni=${proxy.sni}`;
        return trojanLine;
      
      default:
        return null;
    }
  }
}

// QuantumultX格式转换器
export class QuantumultXConverter {
  static convert(proxies, config = {}) {
    const lines = ['[server_local]'];
    
    proxies.forEach(proxy => {
      const qxProxy = this.convertProxy(proxy);
      if (qxProxy) {
        lines.push(qxProxy);
      }
    });

    return lines.join('\n');
  }

  static convertProxy(proxy) {
    switch (proxy.type) {
      case 'ss':
        return `shadowsocks=${proxy.server}:${proxy.port}, method=${proxy.cipher}, password=${proxy.password}, tag=${proxy.name}`;
      
      case 'vmess':
        let vmessLine = `vmess=${proxy.server}:${proxy.port}, method=aes-128-gcm, password=${proxy.uuid}, tag=${proxy.name}`;
        if (proxy.network === 'ws') {
          vmessLine += ', obfs=ws';
          if (proxy['ws-opts']?.path) vmessLine += `, obfs-uri=${proxy['ws-opts'].path}`;
          if (proxy['ws-opts']?.headers?.Host) vmessLine += `, obfs-host=${proxy['ws-opts'].headers.Host}`;
        }
        if (proxy.tls) vmessLine += ', over-tls=true';
        return vmessLine;
      
      case 'trojan':
        return `trojan=${proxy.server}:${proxy.port}, password=${proxy.password}, tag=${proxy.name}`;
      
      default:
        return null;
    }
  }
}

// 主转换函数
export function convertSubscription(proxies, format, config = {}) {
  switch (format.toLowerCase()) {
    case 'clash':
      return ClashConverter.convert(proxies, config);
    case 'surge':
      return SurgeConverter.convert(proxies, config);
    case 'quantumult-x':
    case 'qx':
      return QuantumultXConverter.convert(proxies, config);
    case 'uri':
      return proxies.map(proxy => convertToURI(proxy)).join('\n');
    default:
      throw new Error(`Unsupported format: ${format}`);
  }
}

// 转换为URI格式
function convertToURI(proxy) {
  switch (proxy.type) {
    case 'ss':
      const userInfo = Base64.encode(`${proxy.cipher}:${proxy.password}`);
      return `ss://${userInfo}@${proxy.server}:${proxy.port}#${encodeURIComponent(proxy.name)}`;
    
    case 'vmess':
      const vmessConfig = {
        v: '2',
        ps: proxy.name,
        add: proxy.server,
        port: proxy.port.toString(),
        id: proxy.uuid,
        aid: (proxy.alterId || 0).toString(),
        net: proxy.network || 'tcp',
        type: 'none',
        host: proxy['ws-opts']?.headers?.Host || '',
        path: proxy['ws-opts']?.path || '',
        tls: proxy.tls ? 'tls' : ''
      };
      return `vmess://${Base64.encode(JSON.stringify(vmessConfig))}`;
    
    default:
      return `# Unsupported proxy type: ${proxy.type}`;
  }
}
